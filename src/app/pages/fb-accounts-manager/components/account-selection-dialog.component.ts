import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { TagModule } from 'primeng/tag';
import { DividerModule } from 'primeng/divider';
import {
  FacebookAccountSelectionData,
  FacebookAccountSelectionResult,
} from '../models';
import {
  getAccountStatusLabel,
  getAccountStatusSeverity,
} from '../../../core/types/facebook-accounts-structure.types';

@Component({
  selector: 'app-account-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    DialogModule,
    CheckboxModule,
    TagModule,
    DividerModule,
  ],
  template: `
    <p-dialog
      [visible]="visible"
      [modal]="true"
      [closable]="false"
      [draggable]="false"
      [resizable]="false"
      styleClass="account-selection-dialog"
      header="🔗 Select Facebook Ad Accounts"
    >
      <div class="dialog-content">
        <!-- User Info -->
        <div class="user-info">
          <div class="user-avatar">
            <i class="fab fa-facebook-f"></i>
          </div>
          <div class="user-details">
            <h3>{{ selectionData?.user_info?.name }}</h3>
            <p>{{ selectionData?.user_info?.email }}</p>
          </div>
        </div>

        <p-divider></p-divider>

        <!-- Available Accounts -->
        <div
          *ngIf="selectionData?.available_accounts?.length"
          class="accounts-section"
        >
          <h4>
            <i class="pi pi-check-circle" style="color: #38a169;"></i>
            Available Accounts ({{ selectionData?.available_accounts?.length }})
          </h4>
          <p class="section-description">
            ✨ Select which accounts you want to add to your workspace:
          </p>

          <div class="accounts-list">
            <div
              *ngFor="let account of selectionData?.available_accounts"
              class="account-item available"
            >
              <p-checkbox
                [binary]="true"
                [(ngModel)]="selectedAccounts[account.id]"
                [inputId]="'account-' + account.id"
              >
              </p-checkbox>
              <label [for]="'account-' + account.id" class="account-label">
                <div class="account-info">
                  <div class="account-name">{{ account.name }}</div>
                  <div class="account-meta">
                    <span class="account-id">{{ account.id }}</span>
                    <p-tag
                      [value]="getAccountStatusLabel(account.account_status)"
                      [severity]="
                        getAccountStatusSeverity(account.account_status)
                      "
                    >
                    </p-tag>
                    <span class="account-currency">{{ account.currency }}</span>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <!-- Conflicted Accounts -->
        <div
          *ngIf="selectionData?.conflicted_accounts?.length"
          class="accounts-section"
        >
          <h4>
            <i class="pi pi-exclamation-triangle" style="color: #e53e3e;"></i>
            Conflicted Accounts ({{
              selectionData?.conflicted_accounts?.length
            }})
          </h4>
          <p class="section-description">
            ⚠️ These accounts are already assigned to other users:
          </p>

          <div class="accounts-list">
            <div
              *ngFor="let conflict of selectionData?.conflicted_accounts"
              class="account-item conflicted"
            >
              <div class="conflict-icon">
                <i class="pi pi-lock"></i>
              </div>
              <div class="account-info">
                <div class="account-name">{{ conflict.account.name }}</div>
                <div class="account-meta">
                  <span class="account-id">{{ conflict.account.id }}</span>
                  <p-tag
                    [value]="
                      getAccountStatusLabel(conflict.account.account_status)
                    "
                    [severity]="
                      getAccountStatusSeverity(conflict.account.account_status)
                    "
                  >
                  </p-tag>
                  <span class="account-currency">{{
                    conflict.account.currency
                  }}</span>
                </div>
                <div class="conflict-info">
                  <i class="pi pi-user"></i>
                  Already assigned to:
                  <strong>{{ conflict.existing_user.name }}</strong>
                  <span *ngIf="conflict.existing_user.email"
                    >({{ conflict.existing_user.email }})</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Accounts Message -->
        <div
          *ngIf="
            !selectionData?.available_accounts?.length &&
            !selectionData?.conflicted_accounts?.length
          "
          class="no-accounts"
        >
          <i class="fab fa-facebook-f"></i>
          <h4>No Ad Accounts Found</h4>
          <p>
            This Facebook user doesn't have access to any ad accounts, or all
            accounts are already assigned to other users.
          </p>
        </div>
      </div>

      <ng-template pTemplate="footer">
        <div class="dialog-footer">
          <p-button
            label="Cancel"
            [outlined]="true"
            severity="secondary"
            icon="pi pi-times"
            (onClick)="onCancel()"
          >
          </p-button>
          <p-button
            label="Save Selection"
            severity="primary"
            icon="pi pi-save"
            (onClick)="onConfirm()"
          >
          </p-button>
        </div>
      </ng-template>
    </p-dialog>
  `,
  styles: [
    `
      .dialog-content {
        padding: 0;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(85, 33, 190, 0.1);
      }

      .user-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, #5521be, #e036af);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.4rem;
        box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3);
      }

      .user-details h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #5521be;
        font-family: 'Poppins', sans-serif;
      }

      .user-details p {
        margin: 0.25rem 0 0 0;
        color: #666;
        font-size: 0.9rem;
      }

      .accounts-section {
        margin-bottom: 1.5rem;
      }

      .accounts-section h4 {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #5521be;
        font-family: 'Poppins', sans-serif;
      }

      .accounts-section h4 i {
        font-size: 1.2rem;
      }

      .section-description {
        margin: 0 0 1rem 0;
        color: #666;
        font-size: 0.9rem;
        padding-left: 2rem;
      }

      .accounts-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .account-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.25rem;
        background: white;
        border: 2px solid transparent;
        border-radius: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .account-item.available {
        cursor: pointer;
      }

      .account-item.available:hover {
        border-color: #5521be;
        box-shadow: 0 4px 16px rgba(85, 33, 190, 0.15);
        transform: translateY(-1px);
      }

      .account-item.conflicted {
        background: #fff5f5;
        border-color: #fed7d7;
      }

      .account-label {
        flex: 1;
        cursor: pointer;
      }

      .account-info {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .account-name {
        font-weight: 600;
        font-size: 1rem;
        color: #2d3748;
        font-family: 'Poppins', sans-serif;
      }

      .account-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .account-id {
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.8rem;
        color: #666;
        background: #f7fafc;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
      }

      .account-currency {
        font-size: 0.85rem;
        color: #666;
        background: #f7fafc;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;
      }

      .conflict-icon {
        color: #e53e3e;
        font-size: 1.2rem;
        margin-top: 0.1rem;
      }

      .conflict-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: #e53e3e;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: #fed7d7;
        border-radius: 8px;
      }

      .conflict-info strong {
        color: #c53030;
      }

      .no-accounts {
        text-align: center;
        padding: 3rem 2rem;
        color: #666;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .no-accounts i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #5521be;
      }

      .no-accounts h4 {
        margin: 0 0 0.5rem 0;
        color: #2d3748;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
      }

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
      }

      /* Use default Chainmatic theme checkbox styling - no custom overrides */

      /* Ensure checkbox is properly aligned */
      :host ::ng-deep .account-item p-checkbox {
        flex-shrink: 0;
      }

      :host ::ng-deep .account-item .p-checkbox {
        align-self: flex-start;
        margin-top: 2px;
      }

      :host ::ng-deep .p-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;
      }

      :host ::ng-deep .p-divider {
        margin: 1.5rem 0;
      }

      :host ::ng-deep .p-divider .p-divider-content {
        background: #feecf9;
      }
    `,
  ],
})
export class AccountSelectionDialogComponent implements OnInit {
  @Input() visible = false;
  @Output() result = new EventEmitter<FacebookAccountSelectionResult>();
  selectedAccounts: { [accountId: string]: boolean } = {};
  getAccountStatusLabel = getAccountStatusLabel;
  getAccountStatusSeverity = getAccountStatusSeverity;

  private _selectionData: FacebookAccountSelectionData | null = null;

  get selectionData(): FacebookAccountSelectionData | null {
    return this._selectionData;
  }

  @Input() set selectionData(data: FacebookAccountSelectionData | null) {
    this._selectionData = data;
    this.initializeSelectedAccounts();
  }

  ngOnInit(): void {
    this.initializeSelectedAccounts();
  }

  hasSelectedAccounts(): boolean {
    return Object.values(this.selectedAccounts).some((selected) => selected);
  }

  onConfirm(): void {
    const selectedAccountIds = Object.keys(this.selectedAccounts).filter(
      (accountId) => this.selectedAccounts[accountId],
    );

    this.result.emit({
      selected_accounts: selectedAccountIds,
      confirmed: true,
    });

    this.resetSelection();
  }

  onCancel(): void {
    this.result.emit({
      selected_accounts: [],
      confirmed: false,
    });

    this.resetSelection();
  }

  private initializeSelectedAccounts(): void {
    this.selectedAccounts = {};
    if (this._selectionData?.selected_accounts) {
      this._selectionData.selected_accounts.forEach((accountId) => {
        this.selectedAccounts[accountId] = true;
      });
    }
  }

  private resetSelection(): void {
    this.selectedAccounts = {};
  }
}
