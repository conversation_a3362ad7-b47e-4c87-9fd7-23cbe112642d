import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { interval, startWith, Subject, switchMap, takeUntil } from 'rxjs';
import { MessageService } from 'primeng/api';

import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { TagModule } from 'primeng/tag';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import {
  CREATIVE_FILE_STATUSES,
  CreativeFile,
  CreativeFileFilters,
  CreativeFileStatus,
  formatFileSize,
  getMimeTypeIcon,
  getMimeTypeLabel,
  getStatusLabel,
  getStatusSeverity,
  isActiveStatus,
  MIME_TYPE_FILTERS,
  CreativesUploaderConfigDialogData,
  CreativesUploaderConfigResult,
} from './models';
import { CreativeFileService, CreativesUploaderConfigService } from './services';
import { CreativesUploaderConfigDialogComponent } from './components/creatives-uploader-config-dialog.component';
import { FeatureHeaderComponent } from '../../shared';

@Component({
  selector: 'chm-creatives-uploader',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    SelectModule,
    TagModule,
    ProgressSpinnerModule,
    ToastModule,
    CreativesUploaderConfigDialogComponent,
    FeatureHeaderComponent,
  ],
  providers: [MessageService],
  templateUrl: './creatives-uploader.component.html',
  styleUrls: ['./creatives-uploader.component.css'],
})
export class CreativesUploaderComponent implements OnInit, OnDestroy {
  // Data
  files: CreativeFile[] = [];
  totalRecords = 0;
  loading = false;
  // Filters
  filters: CreativeFileFilters = {};
  statusOptions = CREATIVE_FILE_STATUSES;
  mimeTypeOptions = MIME_TYPE_FILTERS;
  // Template helper methods
  getStatusSeverity = getStatusSeverity;
  getStatusLabel = getStatusLabel;
  formatFileSize = formatFileSize;
  getMimeTypeIcon = getMimeTypeIcon;
  getMimeTypeLabel = getMimeTypeLabel;
  isActiveStatus = isActiveStatus;

  // Configuration Dialog
  showConfigDialog = false;
  configDialogData: CreativesUploaderConfigDialogData | null = null;

  private destroy$ = new Subject<void>();
  // Auto-reload
  private autoReloadInterval = 30000; // 30 seconds

  constructor(
    private creativeFileService: CreativeFileService,
    private messageService: MessageService,
    private creativesUploaderConfigService: CreativesUploaderConfigService,
  ) {}

  ngOnInit(): void {
    this.loadFiles();
    this.setupAutoReload();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadFiles(): void {
    this.loading = true;

    this.creativeFileService
      .getCreativeFiles(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.files = response.data;
          this.totalRecords = response.total_count;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading files:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load files',
          });
          this.loading = false;
        },
      });
  }

  onFilterChange(): void {
    this.loadFiles();
  }

  onStatusFilterChange(status: CreativeFileStatus | null): void {
    this.filters.status = status || undefined;
    this.loadFiles();
  }

  onMimeTypeFilterChange(mimeType: string): void {
    this.filters.mime_type = mimeType || undefined;
    this.loadFiles();
  }

  onSearchChange(event: any): void {
    this.filters.search = event.target.value || undefined;
    this.loadFiles();
  }

  getActiveUploadsCount(): number {
    return this.creativeFileService.getActiveUploadsCount(this.files);
  }

  hasActiveUploads(): boolean {
    return this.creativeFileService.hasActiveUploads(this.files);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getFileTypeClass(mimeType: string): string {
    if (mimeType.startsWith('video/')) return 'file-type-video';
    if (mimeType.startsWith('image/')) return 'file-type-image';
    return 'file-type-other';
  }

  getFacebookCreativeUrl(creativeId: string): string {
    return `https://business.facebook.com/adsmanager/manage/videolibrary/videos/?video_id==${creativeId}`;
  }

  getGoogleDriveUrl(fileId: string): string {
    return `https://drive.google.com/file/d/${fileId}/view`;
  }

  private setupAutoReload(): void {
    // Auto-reload when there are active uploads
    interval(this.autoReloadInterval)
      .pipe(
        startWith(0),
        switchMap(() => {
          const hasActiveUploads = this.creativeFileService.hasActiveUploads(
            this.files,
          );
          return hasActiveUploads ? [true] : [];
        }),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        if (this.creativeFileService.hasActiveUploads(this.files)) {
          this.loadFiles();
        }
      });
  }

  /**
   * Open configuration dialog
   */
  openConfigDialog(): void {
    this.creativesUploaderConfigService.getConfigDialogData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.configDialogData = data;
          this.showConfigDialog = true;
        },
        error: (error) => {
          console.error('Error loading config dialog data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Configuration Error',
            detail: 'Failed to load configuration data.',
            life: 5000
          });
        }
      });
  }

  /**
   * Handle configuration dialog result
   */
  onConfigResult(result: CreativesUploaderConfigResult): void {
    this.showConfigDialog = false;

    if (result.confirmed) {
      this.creativesUploaderConfigService.saveConfig(result.config)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            const accountCount = result.config.accountKeywords.length;
            const message = accountCount === 0
              ? 'Configuration cleared - no accounts configured for Creatives Uploader.'
              : `Successfully configured ${accountCount} Facebook account(s) with keywords.`;

            this.messageService.add({
              severity: 'success',
              summary: 'Configuration Saved',
              detail: message,
              life: 5000
            });
          },
          error: (error) => {
            console.error('Error saving configuration:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Configuration Error',
              detail: 'Failed to save configuration. Please try again.',
              life: 5000
            });
          }
        });
    }
  }
}
